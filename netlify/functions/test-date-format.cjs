// Test function for date format conversion
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Test date validation and formatting functions

const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return "";
  
  // Convert dd/mm/yyyy to Date object
  const parseDate = (dateStr) => {
    if (dateStr.includes("/")) {
      const [day, month, year] = dateStr.split("/");
      return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    } else if (dateStr.includes("-")) {
      return new Date(dateStr);
    }
    return new Date(dateStr);
  };
  
  const start = parseDate(startDate);
  const end = parseDate(endDate);
  
  if (start >= end) return "";
  
  let years = end.getFullYear() - start.getFullYear();
  let months = end.getMonth() - start.getMonth();
  
  if (months < 0) {
    years--;
    months += 12;
  }
  
  const parts = [];
  if (years > 0) {
    parts.push(`${years} ${years === 1 ? 'Year' : 'Years'}`);
  }
  if (months > 0) {
    parts.push(`${months} ${months === 1 ? 'Month' : 'Months'}`);
  }
  
  return parts.length > 0 ? parts.join(' ') : "";
};

exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('Date format test started');

    // Test date format validation and auto-formatting
    const testCases = [
      {
        description: "Auto-format date input: 15012024 -> 15/01/2024",
        input: "15012024",
        expected: "15/01/2024",
        actual: (() => {
          let cleaned = "15012024".replace(/[^\d/]/g, '');
          if (cleaned.length >= 2 && cleaned.charAt(2) !== '/') {
            cleaned = cleaned.substring(0, 2) + '/' + cleaned.substring(2);
          }
          if (cleaned.length >= 5 && cleaned.charAt(5) !== '/') {
            cleaned = cleaned.substring(0, 5) + '/' + cleaned.substring(5);
          }
          if (cleaned.length > 10) {
            cleaned = cleaned.substring(0, 10);
          }
          return cleaned;
        })()
      },
      {
        description: "Validate DD/MM/YYYY format",
        input: "15/01/2024",
        expected: true,
        actual: /^\d{2}\/\d{2}\/\d{4}$/.test("15/01/2024")
      },
      {
        description: "Calculate duration from dd/mm/yyyy dates",
        input: { start: "15/01/2022", end: "15/01/2024" },
        expected: "2 Years",
        actual: calculateDuration("15/01/2022", "15/01/2024")
      },
      {
        description: "Calculate duration with months",
        input: { start: "01/06/2021", end: "01/12/2021" },
        expected: "6 Months",
        actual: calculateDuration("01/06/2021", "01/12/2021")
      },
      {
        description: "Calculate duration with years and months",
        input: { start: "01/09/2018", end: "30/06/2022" },
        expected: "3 Years 9 Months",
        actual: calculateDuration("01/09/2018", "30/06/2022")
      },
      {
        description: "Reject invalid day (32/01/2024)",
        input: "32/01/2024",
        expected: false,
        actual: (() => {
          const [day, month, year] = "32/01/2024".split('/').map(Number);
          const date = new Date(year, month - 1, day);
          return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year;
        })()
      },
      {
        description: "Reject invalid month (15/13/2024)",
        input: "15/13/2024",
        expected: false,
        actual: (() => {
          const [day, month, year] = "15/13/2024".split('/').map(Number);
          const date = new Date(year, month - 1, day);
          return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year;
        })()
      }
    ];

    const results = testCases.map(testCase => ({
      ...testCase,
      passed: testCase.actual === testCase.expected
    }));

    const allPassed = results.every(result => result.passed);

    // Test dropdown value handling
    const dropdownTests = [
      {
        description: "WillingToTravel dropdown should accept 'yes'",
        input: "yes",
        expected: "yes",
        actual: "yes"
      },
      {
        description: "WillingToTravel dropdown should accept 'no'",
        input: "no",
        expected: "no",
        actual: "no"
      }
    ];

    const dropdownResults = dropdownTests.map(testCase => ({
      ...testCase,
      passed: testCase.actual === testCase.expected
    }));

    const allDropdownPassed = dropdownResults.every(result => result.passed);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Date format test completed',
        timestamp: new Date().toISOString(),
        dateTests: {
          allPassed,
          results
        },
        dropdownTests: {
          allPassed: allDropdownPassed,
          results: dropdownResults
        },
        summary: {
          totalTests: results.length + dropdownResults.length,
          passed: results.filter(r => r.passed).length + dropdownResults.filter(r => r.passed).length,
          failed: results.filter(r => !r.passed).length + dropdownResults.filter(r => !r.passed).length
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Test failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
