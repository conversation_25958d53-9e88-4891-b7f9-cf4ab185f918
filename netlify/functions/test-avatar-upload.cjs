// Test function for avatar upload functionality
const fetch = require('node-fetch');

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Create a simple test image as base64 (1x1 pixel PNG)
const createTestImage = () => {
  // This is a 1x1 transparent PNG image encoded as base64
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  return `data:image/png;base64,${base64Image}`;
};

exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('Avatar upload test started');

    // Check environment variables
    const requiredEnvVars = [
      'GCS_PROJECT_ID',
      'GCS_BUCKET_NAME', 
      'GCS_SERVICE_ACCOUNT_EMAIL',
      'GCS_PRIVATE_KEY',
      'NOCODB_URL',
      'NOCODB_TOKEN'
    ];
    
    const envCheck = {};
    const missingVars = [];
    
    requiredEnvVars.forEach(varName => {
      const hasVar = !!process.env[varName];
      envCheck[varName] = hasVar;
      if (!hasVar) {
        missingVars.push(varName);
      }
    });

    console.log('Environment variables check:', envCheck);

    if (missingVars.length > 0) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing required environment variables',
          missing: missingVars,
          envCheck
        })
      };
    }

    // Test data
    const testData = {
      recordId: 'test-record-123', // This would normally come from a real NocoDB record
      fileName: 'test-avatar.png',
      fileData: createTestImage()
    };

    console.log('Test data prepared:', {
      recordId: testData.recordId,
      fileName: testData.fileName,
      fileDataLength: testData.fileData.length
    });

    // Test the avatar upload function
    const uploadResponse = await fetch(`${event.headers.host}/api/upload-avatar`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const uploadResult = await uploadResponse.text();
    
    console.log('Upload response:', {
      status: uploadResponse.status,
      statusText: uploadResponse.statusText,
      response: uploadResult.substring(0, 500) + (uploadResult.length > 500 ? '...' : '')
    });

    let parsedResult = null;
    try {
      parsedResult = JSON.parse(uploadResult);
    } catch (parseError) {
      console.warn('Failed to parse upload response as JSON');
    }

    // Return test results
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Avatar upload test completed',
        timestamp: new Date().toISOString(),
        environment: {
          allEnvVarsPresent: missingVars.length === 0,
          missingVars,
          envCheck
        },
        uploadTest: {
          success: uploadResponse.ok,
          status: uploadResponse.status,
          statusText: uploadResponse.statusText,
          response: parsedResult || uploadResult.substring(0, 200),
          testData: {
            recordId: testData.recordId,
            fileName: testData.fileName,
            fileSize: testData.fileData.length
          }
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Test failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
