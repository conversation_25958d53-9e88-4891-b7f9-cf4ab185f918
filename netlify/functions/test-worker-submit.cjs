exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    // Test the exact same logic as submit-form but with detailed logging
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;
    const brevoApiKey = process.env.BREVO_API_KEY;
    const notificationEmail = process.env.NOTIFICATION_EMAIL;

    console.log('Environment variables check:', {
      hasNocoDBUrl: !!nocoDBUrl,
      hasNocoDBToken: !!nocoDBToken,
      hasBrevoApiKey: !!brevoApiKey,
      hasNotificationEmail: !!notificationEmail,
      nocoDBUrl: nocoDBUrl,
      notificationEmail: notificationEmail
    });

    // Check if environment variables are set
    if (!nocoDBUrl || !nocoDBToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing NocoDB environment variables',
          details: {
            hasNocoDBUrl: !!nocoDBUrl,
            hasNocoDBToken: !!nocoDBToken,
            hasBrevoApiKey: !!brevoApiKey,
            hasNotificationEmail: !!notificationEmail
          }
        })
      };
    }

    // Create test worker form data exactly like the frontend sends (Chinese version)
    const testFormData = {
      FirstName: "明华",
      LastName: "李",
      Email: "<EMAIL>",
      PhoneNumber: "+49 176 12345678",
      City: "柏林",
      Nationality: "中国",
      AvailabilityDate: "15/03/2024",
      WillingToTravel: "yes",
      SalaryExpectation: "4200",
      DesiredPosition: "全栈软件开发工程师",
      EmploymentType: "full_time",
      Job1_Title: "高级前端开发工程师",
      Job1_Company: "德国科技创新有限公司",
      Job1_Duration: "2 Years 3 Months",
      Job1_Tasks: "负责使用React、TypeScript和Node.js开发大型企业级Web应用程序。参与系统架构设计，优化前端性能，实现响应式设计，与后端团队协作开发RESTful API。",
      Job1_Start: "01/02/2022",
      Job1_End: "15/05/2024",
      Job2_Title: "全栈开发实习生",
      Job2_Company: "柏林数字化解决方案公司",
      Job2_Duration: "8 Months",
      Job2_Tasks: "学习并实践现代Web开发技术栈，包括React、Express.js、MongoDB。参与敏捷开发流程，编写单元测试，使用Git进行版本控制。",
      Job2_Start: "01/06/2021",
      Job2_End: "31/01/2022",
      Job3_Title: "软件开发助理",
      Job3_Company: "慕尼黑软件咨询公司",
      Job3_Duration: "4 Months",
      Job3_Tasks: "协助开发团队进行代码审查，参与需求分析，学习软件开发最佳实践，使用JIRA进行项目管理。",
      Job3_Start: "01/02/2021",
      Job3_End: "31/05/2021",
      EducationLevel: "masters_degree",
      // Single education entry without "1" suffix to match NocoDB structure
      Education_Start: "01/10/2022",
      Education_End: "30/09/2024",
      Education_School: "慕尼黑工业大学 (Technische Universität München)",
      Education_Detail: "计算机科学硕士学位，专业方向：软件工程与人工智能。主要课程包括：高级算法设计、机器学习、分布式系统、软件架构模式。毕业论文：基于深度学习的自然语言处理在企业级应用中的实现。GPA: 1.7 (德国评分系统)",
      GermanLevel: "c1",
      OtherLanguages: "英语 (C2), 西班牙语 (B1), 法语 (A2)",
      DrivingLicense: "class_b",
      SkillsAndCerts: "React.js, Vue.js, Angular, Node.js, Express.js, TypeScript, JavaScript, Python, Java, C++, AWS (Certified Solutions Architect), Docker, Kubernetes, MongoDB, PostgreSQL, Redis, Git, Jenkins, JIRA, Agile/Scrum, TDD, GraphQL, REST APIs, Microservices Architecture",
      Language: "zh",
      formType: "worker"
    };

    console.log('Test form data prepared:', {
      formType: testFormData.formType,
      language: testFormData.Language,
      firstName: testFormData.FirstName,
      lastName: testFormData.LastName,
      email: testFormData.Email,
      educationLevel: testFormData.EducationLevel,
      hasEducationDetails: !!(testFormData.Education_Start && testFormData.Education_School),
      jobCount: [testFormData.Job1_Title, testFormData.Job2_Title, testFormData.Job3_Title].filter(Boolean).length,
      dataKeys: Object.keys(testFormData),
      totalFields: Object.keys(testFormData).length
    });

    // Test NocoDB submission
    const formType = testFormData.formType;
    const nocoTableId = formType === 'worker' ? 'mcv0hz2nurqap37' : 'm2yux6be57tujg8';

    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    const nocoApiUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

    console.log('NocoDB submission details:', {
      formType,
      nocoTableId,
      baseUrl,
      nocoApiUrl,
      timestamp: new Date().toISOString()
    });

    let nocoResult = null;
    let avatarResult = null;
    try {
      const nocoResponse = await fetch(nocoApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'xc-token': nocoDBToken,
        },
        body: JSON.stringify(testFormData),
      });

      const nocoResponseText = await nocoResponse.text();
      
      nocoResult = {
        success: nocoResponse.ok,
        status: nocoResponse.status,
        statusText: nocoResponse.statusText,
        response: nocoResponseText,
        headers: Object.fromEntries(nocoResponse.headers.entries())
      };

      console.log('NocoDB response:', nocoResult);

      if (!nocoResponse.ok) {
        console.error('NocoDB Error Details:', {
          status: nocoResponse.status,
          statusText: nocoResponse.statusText,
          response: nocoResponseText,
          url: nocoApiUrl
        });
      } else {
        // Extract record ID for avatar upload test
        let recordId = null;
        try {
          const parsedResponse = JSON.parse(nocoResponseText);
          recordId = parsedResponse.Id || parsedResponse.id;
          console.log('Record ID extracted for avatar test:', recordId);
        } catch (parseError) {
          console.error('Could not parse response to extract record ID:', parseError);
        }

        // Test avatar upload if we have a record ID
        if (recordId) {
          console.log('\n=== TESTING AVATAR UPLOAD ===');

          try {
            // Create a test avatar (1x1 pixel PNG in base64)
            const testAvatarBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
            const testAvatarDataUrl = `data:image/png;base64,${testAvatarBase64}`;

            const avatarData = {
              recordId: recordId,
              fileName: `test-avatar-${recordId}-${Date.now()}.png`,
              fileData: testAvatarDataUrl
            };

            console.log('Avatar upload data prepared:', {
              recordId: avatarData.recordId,
              fileName: avatarData.fileName,
              fileDataLength: avatarData.fileData.length
            });

            // Call the avatar upload function
            const avatarUploadUrl = `${baseUrl}/.netlify/functions/upload-avatar`;
            console.log('Calling avatar upload function:', avatarUploadUrl);

            const avatarResponse = await fetch(avatarUploadUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(avatarData),
            });

            const avatarResponseText = await avatarResponse.text();

            console.log('Avatar upload response:', {
              status: avatarResponse.status,
              statusText: avatarResponse.statusText,
              success: avatarResponse.ok
            });

            avatarResult = {
              success: avatarResponse.ok,
              status: avatarResponse.status,
              statusText: avatarResponse.statusText,
              response: avatarResponseText
            };

            if (avatarResponse.ok) {
              console.log('✅ Avatar upload successful!');
              try {
                const parsedAvatarResult = JSON.parse(avatarResponseText);
                avatarResult.parsedResponse = parsedAvatarResult;
                console.log('Avatar upload result:', {
                  message: parsedAvatarResult.message,
                  avatarUrl: parsedAvatarResult.avatarUrl,
                  recordId: parsedAvatarResult.recordId
                });
              } catch (parseError) {
                console.log('Avatar response (raw):', avatarResponseText);
              }
            } else {
              console.log('❌ Avatar upload failed');
              console.log('Error response:', avatarResponseText);
            }

            // Verify the record was updated with avatar
            console.log('\n=== VERIFYING AVATAR IN NOCODB ===');

            const verifyUrl = `${nocoApiUrl}/${recordId}`;
            console.log('Checking record at:', verifyUrl);

            const verifyResponse = await fetch(verifyUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'xc-token': nocoDBToken,
              },
            });

            if (verifyResponse.ok) {
              const recordData = await verifyResponse.json();
              const verificationData = {
                recordId: recordData.Id || recordData.id,
                hasAvatar: !!(recordData.Avatar || recordData.avatar || recordData.Photo || recordData.photo),
                avatarValue: recordData.Avatar || recordData.avatar || recordData.Photo || recordData.photo || 'No avatar field found'
              };
              avatarResult.verification = verificationData;
              console.log('Record verification:', verificationData);
            } else {
              const verifyErrorText = await verifyResponse.text();
              avatarResult.verification = {
                error: `Could not verify record: ${verifyResponse.status} ${verifyErrorText}`
              };
              console.log('Could not verify record:', verifyResponse.status, verifyErrorText);
            }

          } catch (avatarError) {
            console.error('Avatar upload test failed:', avatarError.message);
            avatarResult = {
              success: false,
              error: avatarError.message,
              stack: avatarError.stack
            };
          }
        } else {
          console.log('⚠️  Skipping avatar upload test - no record ID available');
        }
      }
    } catch (nocoError) {
      nocoResult = {
        success: false,
        error: nocoError.message,
        stack: nocoError.stack
      };
      console.error('NocoDB fetch error:', nocoError);
    }

    // Test Brevo email if NocoDB succeeded and Brevo is configured
    let brevoResult = null;
    if (nocoResult.success && brevoApiKey && notificationEmail) {
      try {
        const emailSubject = `New worker form: ${testFormData.FirstName} ${testFormData.LastName}`;
        const emailHtmlContent = `<p>A new form has been submitted via the WORKER form.</p><pre>${JSON.stringify(testFormData, null, 2)}</pre>`;

        const brevoResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'api-key': brevoApiKey,
          },
          body: JSON.stringify({
            sender: { email: '<EMAIL>', name: 'Sohnus Form' },
            to: [{ email: notificationEmail }],
            subject: emailSubject,
            htmlContent: emailHtmlContent,
          }),
        });

        const brevoResponseText = await brevoResponse.text();
        
        brevoResult = {
          success: brevoResponse.ok,
          status: brevoResponse.status,
          statusText: brevoResponse.statusText,
          response: brevoResponseText
        };

        console.log('Brevo response:', brevoResult);
      } catch (brevoError) {
        brevoResult = {
          success: false,
          error: brevoError.message,
          stack: brevoError.stack
        };
        console.error('Brevo error:', brevoError);
      }
    }

    // Test acknowledgment email functionality
    let ackEmailResult = null;
    if (nocoResult.success && brevoApiKey) {
      try {
        const templateMap = {
          worker: {
            de: 1,
            en: 2,
            zh: 4,
            es: 3,
          },
          company: {
            de: 5,
            en: 8,
            zh: 7,
            es: 6,
          }
        };

        const language = testFormData.Language || 'en';
        const templateId = templateMap[testFormData.formType]?.[language];

        console.log('Acknowledgment email test:', {
          language,
          formType: testFormData.formType,
          templateId,
          userEmail: testFormData.Email,
          availableTemplates: templateMap[testFormData.formType]
        });

        if (templateId) {
          const dynamicParams = testFormData.formType === 'worker'
            ? { FirstName: testFormData.FirstName }
            : { CompanyName: testFormData.CompanyName };

          const ackEmailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'api-key': brevoApiKey,
            },
            body: JSON.stringify({
              to: [{ email: testFormData.Email }],
              templateId: templateId,
              params: dynamicParams,
            }),
          });

          const ackEmailResponseText = await ackEmailResponse.text();

          ackEmailResult = {
            success: ackEmailResponse.ok,
            status: ackEmailResponse.status,
            statusText: ackEmailResponse.statusText,
            response: ackEmailResponseText,
            templateId,
            language,
            dynamicParams
          };

          console.log('Acknowledgment email response:', ackEmailResult);
        } else {
          ackEmailResult = {
            success: false,
            error: 'No template found for language/form type combination',
            language,
            formType: testFormData.formType,
            availableTemplates: templateMap[testFormData.formType]
          };
        }
      } catch (ackEmailError) {
        ackEmailResult = {
          success: false,
          error: ackEmailError.message,
          stack: ackEmailError.stack
        };
        console.error('Acknowledgment email error:', ackEmailError);
      }
    }

    console.log('\n=== FINAL TEST SUMMARY ===');
    console.log('Test completed at:', new Date().toISOString());
    console.log('Results Summary:');
    console.log('  📝 NocoDB Form Submission:', nocoResult?.success ? '✅ SUCCESS' : '❌ FAILED');
    console.log('  🖼️  Avatar Upload:', avatarResult?.success ? '✅ SUCCESS' : (avatarResult ? '❌ FAILED' : '⏭️  SKIPPED'));
    console.log('  📧 Brevo Notification:', brevoResult?.success ? '✅ SUCCESS' : (brevoResult ? '❌ FAILED' : '⏭️  SKIPPED'));
    console.log('  📬 Acknowledgment Email:', ackEmailResult?.success ? '✅ SUCCESS' : (ackEmailResult ? '❌ FAILED' : '⏭️  SKIPPED'));

    if (avatarResult?.verification) {
      console.log('  🔍 Avatar Verification:', avatarResult.verification.hasAvatar ? '✅ FOUND IN NOCODB' : '❌ NOT FOUND IN NOCODB');
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Worker form submission test completed',
        timestamp: new Date().toISOString(),
        testData: testFormData,
        results: {
          nocodb: nocoResult,
          avatar: avatarResult,
          brevo: brevoResult,
          acknowledgmentEmail: ackEmailResult
        },
        environment: {
          hasNocoDBUrl: !!nocoDBUrl,
          hasNocoDBToken: !!nocoDBToken,
          hasBrevoApiKey: !!brevoApiKey,
          hasNotificationEmail: !!notificationEmail,
          nocoApiUrl
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
