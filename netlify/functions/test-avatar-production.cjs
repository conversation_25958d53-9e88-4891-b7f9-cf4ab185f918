// Production avatar upload test - can be called via URL on deployed site
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
};

exports.handler = async (event) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  try {
    console.log('=== PRODUCTION AVATAR UPLOAD TEST ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Environment:', process.env.NODE_ENV || 'unknown');

    // Step 1: Check environment variables
    console.log('\n--- Environment Variables Check ---');
    const requiredEnvVars = [
      'GCS_PROJECT_ID',
      'GCS_BUCKET_NAME', 
      'GCS_SERVICE_ACCOUNT_EMAIL',
      'GCS_PRIVATE_KEY',
      'NOCODB_URL',
      'NOCODB_TOKEN'
    ];

    const envStatus = {};
    const missingVars = [];
    
    requiredEnvVars.forEach(key => {
      const exists = !!process.env[key];
      envStatus[key] = {
        exists,
        length: process.env[key] ? process.env[key].length : 0
      };
      if (!exists) {
        missingVars.push(key);
      }
    });

    console.log('Environment status:', envStatus);

    if (missingVars.length > 0) {
      console.error('❌ Missing environment variables:', missingVars);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing environment variables in production',
          missingVars,
          message: 'Avatar upload cannot work without these environment variables',
          timestamp: new Date().toISOString()
        })
      };
    }

    console.log('✅ All environment variables present');

    // Step 2: Test form submission to get a real record ID
    console.log('\n--- Creating Test Record ---');
    
    const fetch = require('node-fetch');
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;
    const nocoTableId = 'mcv0hz2nurqap37'; // Worker table ID
    
    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    const nocoApiUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

    const testFormData = {
      FirstName: "Production",
      LastName: "AvatarTest",
      Email: "<EMAIL>",
      PhoneNumber: "+49 123 456789",
      City: "Berlin",
      Nationality: "German",
      DesiredPosition: "Avatar Test Position",
      EmploymentType: "full_time",
      GermanLevel: "b2",
      Language: "en",
      formType: "worker"
    };

    console.log('Creating test record...');
    const formResponse = await fetch(nocoApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xc-token': nocoDBToken,
      },
      body: JSON.stringify(testFormData),
    });

    const formResponseText = await formResponse.text();
    console.log('Form response:', {
      status: formResponse.status,
      success: formResponse.ok
    });

    if (!formResponse.ok) {
      console.error('❌ Failed to create test record');
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Failed to create test record',
          details: formResponseText,
          step: 'create_record'
        })
      };
    }

    let recordId = null;
    try {
      const parsedResponse = JSON.parse(formResponseText);
      recordId = parsedResponse.Id || parsedResponse.id;
      console.log('✅ Test record created with ID:', recordId);
    } catch (parseError) {
      console.error('❌ Could not extract record ID');
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Could not extract record ID',
          response: formResponseText
        })
      };
    }

    // Step 3: Test avatar upload
    console.log('\n--- Testing Avatar Upload ---');
    
    const testAvatarBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    const testAvatarDataUrl = `data:image/png;base64,${testAvatarBase64}`;
    
    const avatarData = {
      recordId: recordId,
      fileName: `production-test-avatar-${Date.now()}.png`,
      fileData: testAvatarDataUrl
    };

    console.log('Avatar data prepared:', {
      recordId: avatarData.recordId,
      fileName: avatarData.fileName,
      fileDataLength: avatarData.fileData.length
    });

    // Call the upload-avatar function directly
    const uploadAvatarHandler = require('./upload-avatar.cjs').handler;
    
    const avatarEvent = {
      httpMethod: 'POST',
      body: JSON.stringify(avatarData),
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log('Calling upload-avatar function...');
    const avatarResult = await uploadAvatarHandler(avatarEvent);
    
    console.log('Avatar upload result:', {
      statusCode: avatarResult.statusCode,
      success: avatarResult.statusCode === 200
    });

    let avatarResponseData = null;
    try {
      avatarResponseData = JSON.parse(avatarResult.body);
      console.log('Avatar response data:', avatarResponseData);
    } catch (parseError) {
      console.log('Avatar response (raw):', avatarResult.body);
    }

    // Log detailed error information if avatar upload failed
    if (avatarResult.statusCode !== 200) {
      console.error('❌ Avatar upload failed with details:');
      console.error('  Status Code:', avatarResult.statusCode);
      console.error('  Response Body:', avatarResult.body);
      if (avatarResponseData) {
        console.error('  Error:', avatarResponseData.error);
        console.error('  Details:', avatarResponseData.details);
        console.error('  Step:', avatarResponseData.step);
      }
    }

    // Step 4: Verify record update
    console.log('\n--- Verifying Record Update ---');
    
    const verifyUrl = `${nocoApiUrl}/${recordId}`;
    const verifyResponse = await fetch(verifyUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'xc-token': nocoDBToken,
      },
    });

    let recordData = null;
    let avatarFound = false;
    let avatarValue = null;

    if (verifyResponse.ok) {
      const verifyResponseText = await verifyResponse.text();
      try {
        recordData = JSON.parse(verifyResponseText);
        const possibleAvatarFields = ['Avatar', 'avatar', 'AvatarUrl', 'avatar_url', 'Photo', 'photo'];
        const avatarField = possibleAvatarFields.find(field => recordData[field]);
        
        if (avatarField) {
          avatarFound = true;
          avatarValue = recordData[avatarField];
          console.log('✅ Avatar found in field:', avatarField);
          console.log('🔗 Avatar URL:', avatarValue);
        } else {
          console.log('❌ No avatar found in any field');
        }
      } catch (parseError) {
        console.error('Could not parse record data');
      }
    } else {
      console.error('Could not verify record');
    }

    // Step 5: Summary
    const summary = {
      environmentCheck: '✅ PASSED',
      recordCreation: formResponse.ok ? '✅ PASSED' : '❌ FAILED',
      avatarUpload: avatarResult.statusCode === 200 ? '✅ PASSED' : '❌ FAILED',
      avatarInDatabase: avatarFound ? '✅ FOUND' : '❌ NOT FOUND'
    };

    console.log('\n--- PRODUCTION TEST SUMMARY ---');
    console.log(summary);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Production avatar upload test completed',
        timestamp: new Date().toISOString(),
        summary,
        details: {
          recordId,
          avatarUploadSuccess: avatarResult.statusCode === 200,
          avatarUploadStatusCode: avatarResult.statusCode,
          avatarUploadError: avatarResponseData?.error || null,
          avatarUploadDetails: avatarResponseData?.details || null,
          avatarUploadStep: avatarResponseData?.step || null,
          avatarUrl: avatarResponseData?.avatarUrl || null,
          avatarFoundInDB: avatarFound,
          avatarValue: avatarValue
        },
        environment: {
          hasAllEnvVars: missingVars.length === 0,
          nodeEnv: process.env.NODE_ENV
        }
      })
    };

  } catch (error) {
    console.error('Production avatar test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Production avatar test failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
