// Test function for placeholder text validation
const fs = require('fs');
const path = require('path');

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Load translation files
const loadTranslations = () => {
  const languages = ['en', 'de', 'zh', 'es'];
  const translations = {};
  
  languages.forEach(lang => {
    try {
      const filePath = path.join(__dirname, '../../public/locales', `${lang}.json`);
      const content = fs.readFileSync(filePath, 'utf8');
      translations[lang] = JSON.parse(content);
    } catch (error) {
      console.error(`Failed to load ${lang}.json:`, error.message);
      translations[lang] = null;
    }
  });
  
  return translations;
};

// Check if all required placeholders exist
const validatePlaceholders = (translations) => {
  const requiredPlaceholders = [
    'first_name',
    'last_name',
    'email',
    'phone',
    'city',
    'nationality',
    'willing_to_travel',
    'salary_expectation',
    'desired_position',
    'employment_type',
    'job_title',
    'job_company',
    'job_duration',
    'job_tasks',
    'job1_title',
    'job1_company',
    'job1_duration',
    'job1_tasks',
    'education_level',
    'education_school',
    'education_detail',
    'german_level',
    'other_languages',
    'driving_license',
    'skills_certs'
  ];

  const results = {};
  
  Object.keys(translations).forEach(lang => {
    if (!translations[lang]) {
      results[lang] = { error: 'Translation file not found' };
      return;
    }
    
    const placeholders = translations[lang].worker_form?.placeholders || {};
    const missing = [];
    const present = [];
    const invalid = []; // Placeholders that still contain translation keys
    
    requiredPlaceholders.forEach(key => {
      if (placeholders[key]) {
        present.push(key);
        // Check if placeholder contains translation key patterns
        if (placeholders[key].includes('worker_form.') || placeholders[key].includes('common.')) {
          invalid.push({
            key,
            value: placeholders[key],
            issue: 'Contains translation key instead of actual text'
          });
        }
      } else {
        missing.push(key);
      }
    });
    
    results[lang] = {
      total: requiredPlaceholders.length,
      present: present.length,
      missing: missing.length,
      missingKeys: missing,
      invalidPlaceholders: invalid,
      allValid: missing.length === 0 && invalid.length === 0
    };
  });
  
  return results;
};

// Check field translations
const validateFields = (translations) => {
  const requiredFields = [
    'first_name',
    'last_name',
    'email',
    'phone',
    'city',
    'nationality',
    'avatar',
    'availability_date',
    'willing_to_travel',
    'salary_expectation',
    'desired_position',
    'employment_type',
    'job_title',
    'job_company',
    'job_start',
    'job_end',
    'job_duration',
    'job_tasks',
    'education_level',
    'education_start',
    'education_end',
    'education_school',
    'education_detail',
    'german_level',
    'other_languages',
    'driving_license',
    'skills_certs'
  ];

  const results = {};
  
  Object.keys(translations).forEach(lang => {
    if (!translations[lang]) {
      results[lang] = { error: 'Translation file not found' };
      return;
    }
    
    const fields = translations[lang].worker_form?.fields || {};
    const missing = [];
    const present = [];
    
    requiredFields.forEach(key => {
      if (fields[key]) {
        present.push(key);
      } else {
        missing.push(key);
      }
    });
    
    results[lang] = {
      total: requiredFields.length,
      present: present.length,
      missing: missing.length,
      missingKeys: missing,
      allValid: missing.length === 0
    };
  });
  
  return results;
};

exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('Placeholder validation test started');

    // Load all translation files
    const translations = loadTranslations();
    
    // Validate placeholders
    const placeholderResults = validatePlaceholders(translations);
    
    // Validate field translations
    const fieldResults = validateFields(translations);
    
    // Calculate overall status
    const languages = ['en', 'de', 'zh', 'es'];
    const overallStatus = {
      placeholders: {
        allLanguagesValid: languages.every(lang => placeholderResults[lang]?.allValid),
        validLanguages: languages.filter(lang => placeholderResults[lang]?.allValid),
        invalidLanguages: languages.filter(lang => !placeholderResults[lang]?.allValid)
      },
      fields: {
        allLanguagesValid: languages.every(lang => fieldResults[lang]?.allValid),
        validLanguages: languages.filter(lang => fieldResults[lang]?.allValid),
        invalidLanguages: languages.filter(lang => !fieldResults[lang]?.allValid)
      }
    };

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Placeholder validation completed',
        timestamp: new Date().toISOString(),
        overallStatus,
        placeholderValidation: placeholderResults,
        fieldValidation: fieldResults,
        recommendations: {
          placeholders: overallStatus.placeholders.allLanguagesValid 
            ? "All placeholder texts are properly configured" 
            : `Fix missing/invalid placeholders in: ${overallStatus.placeholders.invalidLanguages.join(', ')}`,
          fields: overallStatus.fields.allLanguagesValid 
            ? "All field translations are complete" 
            : `Add missing field translations in: ${overallStatus.fields.invalidLanguages.join(', ')}`
        }
      })
    };

  } catch (error) {
    console.error('Validation error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Validation failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
