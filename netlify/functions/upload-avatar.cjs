// Avatar upload function for Netlify
const { Storage } = require('@google-cloud/storage');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const os = require('os');

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Environment variables validation
const validateEnvironment = () => {
  const required = [
    'GCS_PROJECT_ID',
    'GCS_BUCKET_NAME', 
    'GCS_SERVICE_ACCOUNT_EMAIL',
    'GCS_PRIVATE_KEY',
    'NOCODB_URL',
    'NOCODB_TOKEN'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Initialize Google Cloud Storage
const initializeStorage = () => {
  const gcsProjectId = process.env.GCS_PROJECT_ID;
  const gcsBucketName = process.env.GCS_BUCKET_NAME;
  
  const storage = new Storage({
    projectId: gcsProjectId,
    credentials: {
      client_email: process.env.GCS_SERVICE_ACCOUNT_EMAIL,
      private_key: process.env.GCS_PRIVATE_KEY.replace(/\\n/g, '\n'),
    },
  });

  return { storage, bucket: storage.bucket(gcsBucketName), bucketName: gcsBucketName };
};

// Parse JSON data with base64 encoded file
const parseJsonData = (event) => {
  try {
    const data = JSON.parse(event.body);

    if (!data.fileData || !data.fileName || !data.recordId) {
      throw new Error('Missing required fields: fileData, fileName, recordId');
    }

    // Validate file size (base64 encoded, so actual size is ~75% of encoded size)
    const estimatedSize = (data.fileData.length * 3) / 4;
    const maxSize = 2 * 1024 * 1024; // 2MB limit
    const minSize = 1024; // 1KB minimum

    if (estimatedSize > maxSize) {
      throw new Error('File too large. Maximum size is 2MB.');
    }

    if (estimatedSize < minSize) {
      throw new Error('File too small. Please upload a valid image.');
    }

    // Validate file type - only allow specific image formats
    const mimeTypeMatch = data.fileData.match(/^data:([^;]+);base64,/);
    if (!mimeTypeMatch) {
      throw new Error('Invalid file format. Please upload a valid image.');
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const mimeType = mimeTypeMatch[1].toLowerCase();
    if (!allowedTypes.includes(mimeType)) {
      throw new Error('Invalid file type. Only JPG and PNG images are allowed.');
    }

    const base64Data = data.fileData.replace(/^data:[^;]+;base64,/, '');

    return {
      recordId: data.recordId,
      fileName: data.fileName,
      mimeType: mimeType,
      fileBuffer: Buffer.from(base64Data, 'base64')
    };
  } catch (error) {
    throw new Error(`Failed to parse request data: ${error.message}`);
  }
};

// Upload buffer to Google Cloud Storage
const uploadToGCS = async (fileData, bucket, bucketName) => {
  // Generate unique filename
  const timestamp = Date.now();
  const fileExtension = fileData.fileName.split('.').pop() || 'jpg';
  const uniqueFileName = `avatars/avatar-${timestamp}.${fileExtension}`;

  console.log('Uploading file to GCS:', {
    originalName: fileData.fileName,
    uniqueName: uniqueFileName,
    size: fileData.fileBuffer.length,
    mimetype: fileData.mimeType
  });

  // Create a temporary file from the buffer
  const tempFilePath = path.join(os.tmpdir(), `upload-${timestamp}.${fileExtension}`);
  fs.writeFileSync(tempFilePath, fileData.fileBuffer);

  try {
    // Upload file to GCS and make it publicly accessible
    await bucket.upload(tempFilePath, {
      destination: uniqueFileName,
      public: true, // Make file publicly accessible
      metadata: {
        contentType: fileData.mimeType,
        // Set long-term browser cache for better performance
        cacheControl: 'public, max-age=31536000', // 1 year cache
      },
    });

    // Construct public URL
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${uniqueFileName}`;

    console.log('File uploaded successfully:', publicUrl);
    return publicUrl;
  } finally {
    // Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
    } catch (cleanupError) {
      console.warn('Failed to cleanup temp file:', cleanupError);
    }
  }
};

// Update NocoDB record with avatar URL
const updateNocoDBRecord = async (recordId, avatarUrl) => {
  const nocoDBUrl = process.env.NOCODB_URL;
  const nocoDBToken = process.env.NOCODB_TOKEN;
  
  // Worker table ID (from existing code)
  const nocoTableId = 'mcv0hz2nurqap37';
  
  const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
  const updateUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records/${recordId}`;

  console.log('Updating NocoDB record:', {
    recordId,
    avatarUrl,
    updateUrl
  });

  const response = await fetch(updateUrl, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'xc-token': nocoDBToken,
    },
    body: JSON.stringify({
      Avatar: avatarUrl
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to update NocoDB record: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  console.log('NocoDB record updated successfully:', result);
  return result;
};

// Main handler function
exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('Avatar upload request received');

    // Validate environment variables
    validateEnvironment();

    // Initialize Google Cloud Storage
    const { bucket, bucketName } = initializeStorage();

    // Parse JSON data with base64 file
    const fileData = parseJsonData(event);

    console.log('File data parsed:', {
      recordId: fileData.recordId,
      fileName: fileData.fileName,
      mimeType: fileData.mimeType,
      fileSize: fileData.fileBuffer.length
    });

    // Upload file to Google Cloud Storage
    const avatarUrl = await uploadToGCS(fileData, bucket, bucketName);

    // Update NocoDB record with avatar URL
    const updateResult = await updateNocoDBRecord(fileData.recordId, avatarUrl);

    // Return success response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Avatar uploaded successfully',
        avatarUrl,
        recordId: fileData.recordId,
        nocodbResponse: updateResult
      })
    };

  } catch (error) {
    console.error('Avatar upload error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to upload avatar',
        details: error.message
      })
    };
  }
};
