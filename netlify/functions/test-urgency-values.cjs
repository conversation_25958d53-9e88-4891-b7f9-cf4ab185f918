exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;

    if (!nocoDBUrl || !nocoDBToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Missing environment variables' })
      };
    }

    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    const companyTableId = 'm2yux6be57tujg8';

    // Test different urgency values
    const urgencyValues = [
      "immediate",
      "urgent",
      "normal",
      "flexible"
    ];

    const results = [];

    for (let i = 0; i < urgencyValues.length; i++) {
      const urgencyValue = urgencyValues[i];
      
      try {
        const testData = {
          CompanyName: "Test Company GmbH",
          ContactPerson: "Test Person",
          ContactEmail: `test${i}@company.com`,
          ContactPhone: "+49 **********",
          CompanyWebsite: "https://test.com",
          NeededPositions: "Test Position",
          NumberOfVacancies: "1",
          WorkLocation: "Berlin",
          RequiredSkills: "Test skills",
          EmploymentModel: "full_time",
          Urgency: urgencyValue,
          JobDescription: "Test job description",
          formType: "company"
        };

        const response = await fetch(`${baseUrl}/api/v2/tables/${companyTableId}/records`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'xc-token': nocoDBToken,
          },
          body: JSON.stringify(testData),
        });

        const responseText = await response.text();
        
        results.push({
          urgencyValue: urgencyValue,
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          response: responseText.substring(0, 200)
        });

        // Clean up successful test records
        if (response.ok) {
          try {
            const responseData = JSON.parse(responseText);
            if (responseData.Id) {
              await fetch(`${baseUrl}/api/v2/tables/${companyTableId}/records/${responseData.Id}`, {
                method: 'DELETE',
                headers: { 'xc-token': nocoDBToken },
              });
            }
          } catch (deleteError) {
            console.log('Could not delete test record:', deleteError.message);
          }
        }

      } catch (error) {
        results.push({
          urgencyValue: urgencyValue,
          success: false,
          error: error.message
        });
      }
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Urgency values test completed',
        timestamp: new Date().toISOString(),
        results: results,
        summary: {
          total: results.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          successfulValues: results.filter(r => r.success).map(r => r.urgencyValue)
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
