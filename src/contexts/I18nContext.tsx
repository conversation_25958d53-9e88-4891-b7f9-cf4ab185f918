import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'de' | 'zh' | 'es';

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load translations for the current language
  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/locales/${language}.json`);
        if (response.ok) {
          const data = await response.json();
          setTranslations(data);
        } else {
          console.error(`Failed to load translations for ${language}`);
          // Fallback to English if current language fails
          if (language !== 'en') {
            const fallbackResponse = await fetch('/locales/en.json');
            if (fallbackResponse.ok) {
              const fallbackData = await fallbackResponse.json();
              setTranslations(fallbackData);
            }
          }
        }
      } catch (error) {
        console.error('Error loading translations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslations();
  }, [language]);

  // Initialize language from localStorage or default to English
  useEffect(() => {
    const savedLanguage = localStorage.getItem('sohnus-language') as Language;
    if (savedLanguage && ['en', 'de', 'zh', 'es'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
    } else {
      // Always default to English
      setLanguage('en');
    }
  }, []);

  // Save language preference to localStorage
  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('sohnus-language', lang);
  };

  // Translation function with nested key support
  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key; // Return the key itself if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  };

  const contextValue: I18nContextType = {
    language,
    setLanguage: handleSetLanguage,
    t,
    isLoading
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// Helper hook for form options with standardized keys
export const useFormOptions = () => {
  const { t } = useI18n();

  return {
    employmentTypes: [
      { key: 'full_time', label: t('worker_form.options.employment_type.full_time') },
      { key: 'part_time', label: t('worker_form.options.employment_type.part_time') },
      { key: 'contract', label: t('worker_form.options.employment_type.contract') },
      { key: 'temporary', label: t('worker_form.options.employment_type.temporary') }
    ],
    educationLevels: [
      { key: 'no_degree', label: t('worker_form.options.education_level.no_degree') },
      { key: 'vocational_training', label: t('worker_form.options.education_level.vocational_training') },
      { key: 'high_school', label: t('worker_form.options.education_level.high_school') },
      { key: 'bachelors_degree', label: t('worker_form.options.education_level.bachelors_degree') },
      { key: 'masters_degree', label: t('worker_form.options.education_level.masters_degree') },
      { key: 'phd', label: t('worker_form.options.education_level.phd') }
    ],
    germanLevels: [
      { key: 'a1', label: t('worker_form.options.german_level.a1') },
      { key: 'a2', label: t('worker_form.options.german_level.a2') },
      { key: 'b1', label: t('worker_form.options.german_level.b1') },
      { key: 'b2', label: t('worker_form.options.german_level.b2') },
      { key: 'c1', label: t('worker_form.options.german_level.c1') },
      { key: 'c2', label: t('worker_form.options.german_level.c2') },
      { key: 'native_speaker', label: t('worker_form.options.german_level.native_speaker') }
    ],
    drivingLicenses: [
      { key: 'none', label: t('worker_form.options.driving_license.none') },
      { key: 'class_b', label: t('worker_form.options.driving_license.class_b') },
      { key: 'class_c1', label: t('worker_form.options.driving_license.class_c1') },
      { key: 'class_ce', label: t('worker_form.options.driving_license.class_ce') },
      { key: 'forklift_license', label: t('worker_form.options.driving_license.forklift_license') }
    ],
    companyEmploymentModels: [
      { key: 'full_time', label: t('company_form.options.employment_model.full_time') },
      { key: 'part_time', label: t('company_form.options.employment_model.part_time') },
      { key: 'contract', label: t('company_form.options.employment_model.contract') },
      { key: 'temporary', label: t('company_form.options.employment_model.temporary') },
      { key: 'internship', label: t('company_form.options.employment_model.internship') }
    ],
    urgencyLevels: [
      { key: 'immediate', label: t('company_form.options.urgency.immediate') },
      { key: 'urgent', label: t('company_form.options.urgency.urgent') },
      { key: 'normal', label: t('company_form.options.urgency.normal') },
      { key: 'flexible', label: t('company_form.options.urgency.flexible') }
    ]
  };
};
