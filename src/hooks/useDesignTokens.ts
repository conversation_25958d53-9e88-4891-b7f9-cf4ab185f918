import { useTheme } from '@/contexts/ThemeContext';

/**
 * Design Token Utility Hook
 * Provides theme-aware CSS classes and utilities for consistent styling
 */
export const useDesignTokens = () => {
  const { currentTheme, isJobSeekerTheme, isEmployerTheme } = useTheme();

  // Base component classes that adapt to theme
  const tokens = {
    // Button variants
    button: {
      primary: `
        bg-primary text-primary-foreground 
        hover:bg-primary/90 active:bg-primary/80
        shadow-button transition-smooth
        focus:ring-2 focus:ring-ring focus:ring-offset-2
      `,
      secondary: `
        bg-secondary text-secondary-foreground 
        hover:bg-secondary/90 active:bg-secondary/80
        shadow-button transition-smooth
        focus:ring-2 focus:ring-ring focus:ring-offset-2
      `,
      accent: `
        bg-accent text-accent-foreground 
        hover:bg-accent/90 active:bg-accent/80
        shadow-button transition-smooth
        focus:ring-2 focus:ring-ring focus:ring-offset-2
      `,
      outline: `
        border border-border bg-background text-foreground
        hover:bg-accent hover:text-accent-foreground
        transition-smooth focus:ring-2 focus:ring-ring focus:ring-offset-2
      `,
      ghost: `
        text-foreground hover:bg-accent hover:text-accent-foreground
        transition-smooth focus:ring-2 focus:ring-ring focus:ring-offset-2
      `
    },

    // Card variants
    card: {
      default: `
        bg-card text-card-foreground rounded-lg border border-border
        shadow-card transition-smooth
      `,
      elevated: `
        bg-card text-card-foreground rounded-lg border border-border
        shadow-soft hover:shadow-card transition-smooth
      `,
      interactive: `
        bg-card text-card-foreground rounded-lg border border-border
        shadow-card hover:shadow-soft transition-smooth
        cursor-pointer focus:ring-2 focus:ring-ring focus:ring-offset-2
      `
    },

    // Form elements
    form: {
      input: `
        bg-form-background border border-form-border rounded-md
        text-foreground placeholder:text-muted-foreground
        focus:border-form-focus focus:ring-2 focus:ring-form-focus/20
        transition-smooth
      `,
      label: `
        text-sm font-medium text-foreground
      `,
      error: `
        text-sm text-form-error
      `,
      success: `
        text-sm text-form-success
      `,
      fieldset: `
        space-y-2
      `
    },

    // Layout components
    layout: {
      section: `
        py-spacing-2xl px-spacing-md sm:px-spacing-lg lg:px-spacing-xl
      `,
      container: `
        max-w-7xl mx-auto
      `,
      grid: `
        grid gap-spacing-lg
      `
    },

    // Typography
    typography: {
      h1: `
        text-4xl font-bold text-foreground tracking-tight
      `,
      h2: `
        text-3xl font-bold text-foreground tracking-tight
      `,
      h3: `
        text-2xl font-semibold text-foreground tracking-tight
      `,
      h4: `
        text-xl font-semibold text-foreground
      `,
      body: `
        text-base text-foreground leading-relaxed
      `,
      caption: `
        text-sm text-muted-foreground
      `,
      lead: `
        text-lg text-muted-foreground leading-relaxed
      `
    },

    // Theme-specific utilities
    theme: {
      gradient: {
        hero: 'bg-gradient-hero',
        accent: 'bg-gradient-accent',
        card: 'bg-gradient-card'
      },
      shadow: {
        soft: 'shadow-soft',
        card: 'shadow-card',
        button: 'shadow-button'
      }
    }
  };

  // Theme-specific class modifiers
  const themeClasses = {
    // Classes that change based on theme
    heroSection: isEmployerTheme 
      ? 'employer-hero-adjustments' 
      : 'job-seeker-hero-adjustments',
    
    formSection: isEmployerTheme 
      ? 'employer-form-styling' 
      : 'job-seeker-form-styling',
    
    ctaButton: isEmployerTheme 
      ? 'employer-cta-emphasis' 
      : 'job-seeker-cta-emphasis'
  };

  // Utility functions
  const getThemeClass = (baseClass: string, themeVariants?: { jobSeeker?: string; employer?: string }) => {
    if (!themeVariants) return baseClass;
    
    const variant = isEmployerTheme ? themeVariants.employer : themeVariants.jobSeeker;
    return variant ? `${baseClass} ${variant}` : baseClass;
  };

  const combineClasses = (...classes: (string | undefined | null | false)[]) => {
    return classes.filter(Boolean).join(' ').replace(/\s+/g, ' ').trim();
  };

  return {
    tokens,
    themeClasses,
    currentTheme,
    isJobSeekerTheme,
    isEmployerTheme,
    getThemeClass,
    combineClasses
  };
};
