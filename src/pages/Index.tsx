import { useState, useEffect } from "react";
import HeroSection from "@/components/HeroSection";
import FeaturesSection from "@/components/FeaturesSection";
import FormSection from "@/components/FormSection";
import TeamSection from "@/components/TeamSection";
import Footer from "@/components/Footer";
import WelcomeModal from "@/components/WelcomeModal";
import FloatingActionButton from "@/components/FloatingActionButton";
import SEOHead from "@/components/SEO/SEOHead";
import PerformanceOptimizer from "@/components/SEO/PerformanceOptimizer";
import { useStructuredData } from "@/components/SEO/StructuredData";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import { useNavigation } from "@/contexts/NavigationContext";

const Index = () => {
  const [showModal, setShowModal] = useState(false);
  const { scrollToFormSection } = useFormNavigation();
  const { shouldShowModal } = useNavigation();
  const { getOrganizationSchema, getJobBoardSchema, getServiceSchema, getBreadcrumbSchema, getFAQSchema } = useStructuredData();

  useEffect(() => {
    // Show modal only if user arrived via external navigation
    if (shouldShowModal()) {
      setShowModal(true);
    }
  }, [shouldShowModal]);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleFormSectionClick = () => {
    scrollToFormSection();
  };

  // Combine structured data schemas
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      getOrganizationSchema(),
      getJobBoardSchema(),
      getServiceSchema(),
      getBreadcrumbSchema(),
      getFAQSchema()
    ]
  };

  return (
    <>
      <SEOHead
        structuredData={structuredData}
        alternateLanguages={{
          'en': 'https://sohnus.com/',
          'de': 'https://sohnus.com/',
          'zh': 'https://sohnus.com/'
        }}
      />
      <PerformanceOptimizer />
      <div className="min-h-screen">
        <WelcomeModal isOpen={showModal} onClose={handleCloseModal} />
        <HeroSection />
        <FeaturesSection />
        <FormSection />
        <TeamSection />
        <Footer />
        <FloatingActionButton onFormSectionClick={handleFormSectionClick} />
      </div>
    </>
  );
};

export default Index;
