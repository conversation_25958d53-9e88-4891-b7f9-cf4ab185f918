import { useI18n } from "@/contexts/I18nContext";

const FeaturesSection = () => {
  const { t } = useI18n();

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-background" aria-labelledby="features-heading">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <header className="text-center mb-16">
          <h2 id="features-heading" className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('features.section_title') || 'Key Features'}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            {t('features.section_subtitle') || 'Discover how our AI-powered platform revolutionizes job searching and hiring'}
          </p>
        </header>

        {/* Features Grid */}
        <div className="space-y-24 lg:space-y-32">
          {/* Feature 1 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.feature1_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.feature1_description')}
              </p>
            </div>
            <div className="group relative">
              <div className="relative overflow-hidden rounded-xl transition-smooth">
                <img
                  src="/feature-1.png"
                  alt="Voice-powered job profile creation - Speak naturally and let AI build your professional profile"
                  className="w-full h-64 sm:h-72 lg:h-80 object-contain transition-smooth group-hover:scale-105 group-hover:opacity-90"
                />
              </div>
            </div>
          </article>

          {/* Feature 2 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="lg:order-2 space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.feature2_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.feature2_description')}
              </p>
            </div>
            <div className="lg:order-1 group relative">
              <div className="relative overflow-hidden rounded-xl transition-smooth">
                <img
                  src="/feature-2.png"
                  alt="Smart job matching - AI-powered algorithms match you with the perfect opportunities"
                  className="w-full h-64 sm:h-72 lg:h-80 object-contain transition-smooth group-hover:scale-105 group-hover:opacity-90"
                />
              </div>
            </div>
          </article>

          {/* Feature 3 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.feature3_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.feature3_description')}
              </p>
            </div>
            <div className="group relative">
              <div className="relative overflow-hidden rounded-xl shadow-card transition-smooth hover:shadow-soft hover:scale-105">
                <img
                  src="/feature-3.png"
                  alt="Career success and growth - Build a better future with personalized job recommendations"
                  className="w-full h-64 sm:h-72 lg:h-80 object-cover transition-smooth group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-secondary/20 to-transparent opacity-0 group-hover:opacity-100 transition-smooth"></div>
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
