import { useState, useEffect, useRef } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Users, Building2, FileText } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useFormTab, TabType } from "@/contexts/FormTabContext";
import WorkerFormSection from "./WorkerFormSection";
import CompanyFormSection from "./CompanyFormSection";

const FormSection = () => {
  const { t } = useI18n();
  const { preferredTab, getDefaultTabForPage, clearPreferredTab } = useFormTab();
  const [activeTab, setActiveTab] = useState<TabType>("worker");
  const [userHasManuallyChanged, setUserHasManuallyChanged] = useState(false);
  const isInitialMount = useRef(true);

  // Set initial tab based on page on first mount
  useEffect(() => {
    if (isInitialMount.current) {
      const defaultTab = getDefaultTabForPage();
      setActiveTab(defaultTab);
      isInitialMount.current = false;
    }
  }, [getDefaultTabForPage]);

  // Handle preferred tab changes (from navigation actions)
  useEffect(() => {
    if (preferredTab && !isInitialMount.current) {
      // Always respect programmatic navigation (overrides user manual changes)
      setActiveTab(preferredTab);
      setUserHasManuallyChanged(false); // Reset manual change flag

      // Clear the preferred tab after applying it
      setTimeout(() => {
        clearPreferredTab();
      }, 100);
    }
  }, [preferredTab, clearPreferredTab]);

  // Handle manual tab changes by user
  const handleTabChange = (newTab: string) => {
    const tabType = newTab as TabType;
    setActiveTab(tabType);
    setUserHasManuallyChanged(true);

    // Clear any preferred tab when user manually changes
    clearPreferredTab();
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30" data-form-section>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center">
              <FileText className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('forms.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            {t('forms.subtitle')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8 h-16 bg-blue-100 p-2 rounded-xl text-gray-700">
              <TabsTrigger
                value="worker"
                className="flex items-center gap-2 h-full text-base font-semibold rounded-lg transition-all duration-300 bg-transparent text-gray-700 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg data-[state=active]:scale-[1.02] hover:bg-yellow-300/50"
              >
                <Users className="h-5 w-5" />
                {t('forms.tab_worker')}
              </TabsTrigger>
              <TabsTrigger
                value="company"
                className="flex items-center gap-2 h-full text-base font-semibold rounded-lg transition-all duration-300 bg-transparent text-gray-700 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg data-[state=active]:scale-[1.02] hover:bg-yellow-300/50"
              >
                <Building2 className="h-5 w-5" />
                {t('forms.tab_company')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="worker" className="mt-0">
              <div className="relative">
                <WorkerFormSection />
              </div>
            </TabsContent>

            <TabsContent value="company" className="mt-0">
              <div className="relative">
                <CompanyFormSection />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </section>
  );
};

export default FormSection;
