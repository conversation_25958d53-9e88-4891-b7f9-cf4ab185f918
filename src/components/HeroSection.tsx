import { But<PERSON> } from "@/components/ui/button";
import { Star, CheckCircle, Award } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useNavigate, useLocation } from "react-router-dom";
import { useNavigation } from "@/contexts/NavigationContext";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import LanguageSwitcher from "./LanguageSwitcher";

const Header = () => {
  const { t } = useI18n();
  const navigate = useNavigate();
  const location = useLocation();
  const { markInternalNavigation } = useNavigation();
  const { scrollToFormSection } = useFormNavigation();

  // Determine if we're on the hiring page
  const isHiringPage = location.pathname === '/hiring';

  // Toggle button text and navigation (localized)
  const toggleButtonText = isHiringPage
    ? t('navigation.toggle_job_seeking')
    : t('navigation.toggle_hiring');
  const toggleButtonTextShort = isHiringPage
    ? t('navigation.toggle_job_seeking_short')
    : t('navigation.toggle_hiring_short');
  const toggleDestination = isHiringPage ? "/" : "/hiring";

  const handleToggleClick = () => {
    // Mark this as internal navigation to prevent modal
    markInternalNavigation('header-toggle');
    navigate(toggleDestination);
  };

  return (
    <header className="relative z-10 w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={() => window.location.href = '/'}
              className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
              aria-label="Go to homepage"
            >
              <img
                src="/logo.png"
                alt="Sohnus - AI-powered job platform connecting workers and employers in Germany"
                className="h-8 md:h-10 w-auto"
              />
            </button>
          </div>

          {/* Navigation and Actions */}
          <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
            {/* Toggle Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={handleToggleClick}
              className="border-secondary/30 text-secondary hover:bg-secondary/10 hover:border-secondary/50 transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3 md:px-4 focus:ring-2 focus:ring-secondary focus:ring-offset-2"
              aria-label={`Switch to ${toggleButtonText.toLowerCase()} view`}
            >
              <span className="hidden sm:inline">{toggleButtonText}</span>
              <span className="sm:hidden">{toggleButtonTextShort}</span>
            </Button>

            {/* Language Switcher */}
            <LanguageSwitcher />

            {/* Get Started Button */}
            <Button
              size="sm"
              variant="secondary"
              aria-label="Get started with Sohnus"
              className="text-xs sm:text-sm px-2 sm:px-3 md:px-4"
              onClick={() => {
                // Use smart navigation that automatically selects the right tab
                scrollToFormSection();
              }}
            >
              <span className="hidden sm:inline">{t('forms.title')}</span>
              <span className="sm:hidden">Start</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

const HeroSection = () => {
  const { t } = useI18n();
  const { scrollToFormSection } = useFormNavigation();

  return (
    <section className="relative overflow-hidden bg-gradient-hero">
      <Header />
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-0 min-h-[600px]">
          {/* Content */}
          <div className="pt-8 pb-20 px-4 sm:px-6 lg:px-8 space-y-8 flex flex-col justify-center">
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-secondary leading-tight">
                {t('hero.title')}
                <br />
                <span className="text-secondary/90">{t('hero.title_powered')}</span>
              </h1>
              <p className="text-lg sm:text-xl text-secondary/80 max-w-2xl">
                {t('hero.subtitle')}
                <br />
                {t('hero.subtitle_2')}
              </p>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button
                size="lg"
                variant="secondary"
                onClick={() => {
                  // Use smart navigation that automatically selects the right tab
                  scrollToFormSection();
                }}
              >
                {t('hero.cta_worker')}
              </Button>
              {/* <Button size="lg" variant="outline" className="border-secondary/20 text-secondary hover:bg-secondary/10">
                Learn More
              </Button> */}
            </div>

            {/* Trust indicators */}
            <div className="flex items-center gap-6 pt-4">
              <div className="flex items-center gap-2">
                <div className="flex -space-x-1">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="h-5 w-5 fill-secondary text-secondary" />
                  ))}
                </div>
                <span className="text-secondary/80 font-medium">{t('hero.trust_indicator')}</span>
              </div>
            </div>
          </div>

          {/* Hero Image with floating elements */}
          <div className="relative flex items-end">
            <div className="relative w-full">
              <img 
                src="/lovable-uploads/3db855a8-1785-4766-9299-1c6033715fe4.png" 
                alt="Professional worker receiving AI-powered job matching notifications showing 2,500+ interviews and 98% success rate"
                className="w-full h-auto block object-bottom"
              />
              
              {/* Floating UI elements */}
              <div className="absolute top-4 right-4 bg-card p-3 rounded-lg shadow-soft max-w-[200px]">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">2,500+ Interviews</span>
                </div>
                <p className="text-xs text-muted-foreground">AI-powered matching</p>
              </div>

              <div className="absolute bottom-4 left-4 bg-card p-3 rounded-lg shadow-soft max-w-[180px]">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="h-4 w-4 text-accent" fill="currentColor" />
                  <span className="text-sm font-medium">Thanks for sharing!</span>
                </div>
                <p className="text-xs text-muted-foreground">Your profile is saved for job matching</p>
              </div>

              {<div className="absolute top-1/2 -left-4 bg-accent text-accent-foreground p-3 rounded-lg shadow-soft">
                <div className="text-center">
                  <div className="text-2xl font-bold">98%</div>
                  <div className="text-xs">Success Rate</div>
                </div>
              </div>}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;