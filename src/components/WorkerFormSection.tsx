import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Send, Loader2 } from "lucide-react";
import { useI18n, useFormOptions } from "@/contexts/I18nContext";
import { useDesignTokens } from "@/hooks/useDesignTokens";

const WorkerFormSection = () => {
  const { t, language } = useI18n();
  const formOptions = useFormOptions();
  const { tokens, themeClasses, combineClasses } = useDesignTokens();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    FirstName: "",
    LastName: "",
    Email: "",
    PhoneNumber: "",
    City: "",
    Nationality: "",
    AvailabilityDate: "",
    WillingToTravel: "",
    SalaryExpectation: "",
    DesiredPosition: "",
    EmploymentType: "",
    Job1_Title: "",
    Job1_Company: "",
    Job1_Duration: "",
    Job1_Tasks: "",
    Job1_Start: "",
    Job1_End: "",
    Job2_Title: "",
    Job2_Company: "",
    Job2_Duration: "",
    Job2_Tasks: "",
    Job2_Start: "",
    Job2_End: "",
    Job3_Title: "",
    Job3_Company: "",
    Job3_Duration: "",
    Job3_Tasks: "",
    Job3_Start: "",
    Job3_End: "",
    EducationLevel: "",
    // Single education entry without "1" suffix to match NocoDB structure
    Education_Start: "",
    Education_End: "",
    Education_School: "",
    Education_Detail: "",
    GermanLevel: "",
    OtherLanguages: "",
    DrivingLicense: "",
    SkillsAndCerts: "",
    Language: language,
    formType: "worker"
  });
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [jobCount, setJobCount] = useState(1); // Track how many job entries to show
  const { toast } = useToast();

  // Sync language field when user changes language
  useEffect(() => {
    setFormData(prev => ({ ...prev, Language: language }));
  }, [language]);

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digit characters except + at the beginning
    const cleaned = value.replace(/[^\d+]/g, '');

    // If it starts with +49, format as +49 XXX XXXXXX
    if (cleaned.startsWith('+49')) {
      const digits = cleaned.slice(3);
      if (digits.length <= 3) {
        return `+49 ${digits}`;
      } else if (digits.length <= 9) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3, 9)}`;
      }
    }

    // If it starts with 0, assume German number and convert to +49
    if (cleaned.startsWith('0')) {
      const digits = cleaned.slice(1);
      if (digits.length <= 3) {
        return `+49 ${digits}`;
      } else if (digits.length <= 9) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3, 9)}`;
      }
    }

    // For other formats, just clean and return
    return cleaned;
  };

  const handleInputChange = (field: string, value: string) => {
    if (field === 'PhoneNumber') {
      const formatted = formatPhoneNumber(value);
      setFormData(prev => ({ ...prev, [field]: formatted }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type - only allow specific image formats
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type.toLowerCase())) {
        toast({
          title: t('common.error'),
          description: t('worker_form.avatar_invalid_format'),
          variant: "destructive",
        });
        // Clear the input
        e.target.value = '';
        return;
      }

      // Validate file size (2MB limit for better performance)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        toast({
          title: t('common.error'),
          description: t('worker_form.avatar_too_large'),
          variant: "destructive",
        });
        // Clear the input
        e.target.value = '';
        return;
      }

      // Validate minimum file size (to avoid tiny/corrupted images)
      const minSize = 1024; // 1KB
      if (file.size < minSize) {
        toast({
          title: t('common.error'),
          description: t('worker_form.avatar_too_small'),
          variant: "destructive",
        });
        // Clear the input
        e.target.value = '';
        return;
      }

      setAvatarFile(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeAvatar = () => {
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  // Custom date input handler for DD/MM/YYYY format (matching NocoDB format)
  const handleDateInput = (field: string, value: string) => {
    // Remove any non-digit characters except / and -
    let cleaned = value.replace(/[^\d/-]/g, '');

    // Auto-format as user types for DD/MM/YYYY format
    if (cleaned.length >= 2 && cleaned.charAt(2) !== '/' && cleaned.charAt(2) !== '-') {
      cleaned = cleaned.substring(0, 2) + '/' + cleaned.substring(2);
    }
    if (cleaned.length >= 5 && cleaned.charAt(5) !== '/' && cleaned.charAt(5) !== '-') {
      cleaned = cleaned.substring(0, 5) + '/' + cleaned.substring(5);
    }

    // Limit to DD/MM/YYYY format (10 characters)
    if (cleaned.length > 10) {
      cleaned = cleaned.substring(0, 10);
    }

    // Basic validation for day and month ranges
    if (cleaned.length >= 2) {
      const day = parseInt(cleaned.substring(0, 2));
      if (day > 31 || day < 1) {
        return; // Don't update if invalid day
      }
    }
    if (cleaned.length >= 5) {
      const month = parseInt(cleaned.substring(3, 5));
      if (month > 12 || month < 1) {
        return; // Don't update if invalid month
      }
    }

    // Update form data
    handleInputChange(field, cleaned);

    // If it's a complete date, calculate duration for job fields
    if (cleaned.length === 10 && cleaned.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
      // Validate the complete date
      const [day, month, year] = cleaned.split('/').map(Number);
      const date = new Date(year, month - 1, day);
      if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
        return; // Invalid date
      }

      const jobMatch = field.match(/^(Job\d+)_(Start|End)$/);
      if (jobMatch) {
        const jobPrefix = jobMatch[1];
        const startField = `${jobPrefix}_Start`;
        const endField = `${jobPrefix}_End`;
        const durationField = `${jobPrefix}_Duration`;

        const startDate = field === startField ? cleaned : (formData as any)[startField];
        const endDate = field === endField ? cleaned : (formData as any)[endField];

        if (startDate && endDate && startDate.length === 10 && endDate.length === 10) {
          const duration = calculateDuration(startDate, endDate);
          setFormData(prev => ({ ...prev, [durationField]: duration }));
        }
      }
    }
  };

  const addJobEntry = () => {
    if (jobCount < 3) {
      setJobCount(jobCount + 1);
    }
  };

  const removeJobEntry = (jobNumber: number) => {
    if (jobCount > 1 && jobNumber > 1) {
      // Clear the job data for the removed entry
      const jobPrefix = `Job${jobNumber}_`;
      const clearedData = { ...formData };
      Object.keys(clearedData).forEach(key => {
        if (key.startsWith(jobPrefix)) {
          (clearedData as any)[key] = "";
        }
      });
      setFormData(clearedData);
      setJobCount(jobCount - 1);
    }
  };

  // Education functionality removed - only one education entry supported

  // Calculate duration from start and end dates (dd/mm/yyyy format)
  const calculateDuration = (startDate: string, endDate: string): string => {
    if (!startDate || !endDate) return "";

    // Convert dd/mm/yyyy to Date object
    const parseDate = (dateStr: string): Date => {
      if (dateStr.includes("/")) {
        const [day, month, year] = dateStr.split("/");
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      } else if (dateStr.includes("-")) {
        return new Date(dateStr);
      }
      return new Date(dateStr);
    };

    const start = parseDate(startDate);
    const end = parseDate(endDate);

    if (start >= end) return "";

    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();

    if (months < 0) {
      years--;
      months += 12;
    }

    const parts = [];
    if (years > 0) {
      parts.push(`${years} ${years === 1 ? 'Year' : 'Years'}`);
    }
    if (months > 0) {
      parts.push(`${months} ${months === 1 ? 'Month' : 'Months'}`);
    }

    return parts.length > 0 ? parts.join(' ') : "";
  };



  // Render individual job entry
  const renderJobEntry = (jobNumber: number) => {
    const isRequired = jobNumber === 1; // Only first job is required
    const jobPrefix = `Job${jobNumber}_`;

    return (
      <div key={jobNumber} className="border rounded-lg p-4 space-y-4 relative">
        <div className="flex justify-between items-center">
          <h4 className="text-md font-semibold text-foreground">
            {t('worker_form.job_entry')} {jobNumber}
          </h4>
          {jobNumber > 1 && (
            <button
              type="button"
              onClick={() => removeJobEntry(jobNumber)}
              className="text-red-500 hover:text-red-700 text-sm"
            >
              {t('common.remove')}
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor={`${jobPrefix}Title`}>
              {t('worker_form.fields.job_title')} {isRequired && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id={`${jobPrefix}Title`}
              value={(formData as any)[`${jobPrefix}Title`]}
              onChange={(e) => handleInputChange(`${jobPrefix}Title`, e.target.value)}
              placeholder={t('worker_form.placeholders.job_title')}
              required={isRequired}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`${jobPrefix}Company`}>
              {t('worker_form.fields.job_company')} {isRequired && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id={`${jobPrefix}Company`}
              value={(formData as any)[`${jobPrefix}Company`]}
              onChange={(e) => handleInputChange(`${jobPrefix}Company`, e.target.value)}
              placeholder={t('worker_form.placeholders.job_company')}
              required={isRequired}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`${jobPrefix}Start`}>
              {t('worker_form.fields.job_start')} {isRequired && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id={`${jobPrefix}Start`}
              type="text"
              value={(formData as any)[`${jobPrefix}Start`]}
              onChange={(e) => handleDateInput(`${jobPrefix}Start`, e.target.value)}
              placeholder="DD/MM/YYYY"
              maxLength={10}
              required={isRequired}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`${jobPrefix}End`}>
              {t('worker_form.fields.job_end')} {isRequired && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id={`${jobPrefix}End`}
              type="text"
              value={(formData as any)[`${jobPrefix}End`]}
              onChange={(e) => handleDateInput(`${jobPrefix}End`, e.target.value)}
              placeholder="DD/MM/YYYY"
              maxLength={10}
              required={isRequired}
            />
          </div>

          {/* Duration display (calculated automatically) */}
          {(formData as any)[`${jobPrefix}Duration`] && (
            <div className="space-y-2">
              <Label>{t('worker_form.fields.job_duration')}</Label>
              <div className="px-3 py-2 bg-gray-50 border rounded-md text-sm text-gray-700">
                {(formData as any)[`${jobPrefix}Duration`]}
              </div>
            </div>
          )}

          <div className="md:col-span-2 space-y-2">
            <Label htmlFor={`${jobPrefix}Tasks`}>
              {t('worker_form.fields.job_tasks')} {isRequired && <span className="text-red-500">*</span>}
            </Label>
            <Textarea
              id={`${jobPrefix}Tasks`}
              value={(formData as any)[`${jobPrefix}Tasks`]}
              onChange={(e) => handleInputChange(`${jobPrefix}Tasks`, e.target.value)}
              placeholder={t('worker_form.placeholders.job_tasks')}
              className="min-h-[80px]"
              required={isRequired}
            />
          </div>
        </div>
      </div>
    );
  };

  // Render single education entry (only one education entry supported)
  // Updated to match NocoDB structure: Education fields without "1" suffix
  const renderEducationEntry = () => {
    const educationPrefix = `Education_`;

    return (
      <div className="border rounded-lg p-4 space-y-4 relative">
        <div className="flex justify-between items-center">
          <h4 className="text-md font-semibold text-foreground">
            {t('worker_form.education_details')}
          </h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor={`${educationPrefix}Start`}>
              {t('worker_form.fields.education_start')} <span className="text-red-500">*</span>
            </Label>
            <Input
              id={`${educationPrefix}Start`}
              type="text"
              value={(formData as any)[`${educationPrefix}Start`]}
              onChange={(e) => handleDateInput(`${educationPrefix}Start`, e.target.value)}
              placeholder="DD/MM/YYYY"
              maxLength={10}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`${educationPrefix}End`}>
              {t('worker_form.fields.education_end')} <span className="text-red-500">*</span>
            </Label>
            <Input
              id={`${educationPrefix}End`}
              type="text"
              value={(formData as any)[`${educationPrefix}End`]}
              onChange={(e) => handleDateInput(`${educationPrefix}End`, e.target.value)}
              placeholder="DD/MM/YYYY"
              maxLength={10}
              required
            />
          </div>

          <div className="md:col-span-2 space-y-2">
            <Label htmlFor={`${educationPrefix}School`}>
              {t('worker_form.fields.education_school')} <span className="text-red-500">*</span>
            </Label>
            <Input
              id={`${educationPrefix}School`}
              value={(formData as any)[`${educationPrefix}School`]}
              onChange={(e) => handleInputChange(`${educationPrefix}School`, e.target.value)}
              placeholder={t('worker_form.placeholders.education_school')}
              required
            />
          </div>

          <div className="md:col-span-2 space-y-2">
            <Label htmlFor={`${educationPrefix}Detail`}>
              {t('worker_form.fields.education_detail')} <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id={`${educationPrefix}Detail`}
              value={(formData as any)[`${educationPrefix}Detail`]}
              onChange={(e) => handleInputChange(`${educationPrefix}Detail`, e.target.value)}
              placeholder={t('worker_form.placeholders.education_detail')}
              className="min-h-[80px]"
              required
            />
          </div>
        </div>
      </div>
    );
  };

  const validateForm = () => {
    // Basic phone number validation
    const phoneRegex = /^\+49\s\d{3}\s\d{6,9}$/;
    if (!phoneRegex.test(formData.PhoneNumber)) {
      toast({
        title: t('common.error'),
        description: "Please enter a valid German phone number (e.g., +49 123 456789)",
        variant: "destructive",
      });
      return false;
    }

    // Check required fields
    const requiredFields = ['FirstName', 'LastName', 'Email', 'PhoneNumber', 'DesiredPosition', 'EmploymentType'];
    for (const field of requiredFields) {
      if (!formData[field as keyof typeof formData].trim()) {
        toast({
          title: t('common.error'),
          description: `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`,
          variant: "destructive",
        });
        return false;
      }
    }

    // Avatar is now required
    if (!avatarFile) {
      toast({
        title: t('common.error'),
        description: t('worker_form.avatar_required'),
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const uploadAvatar = async (recordId: string) => {
    if (!avatarFile) return null;

    try {
      // Convert file to base64
      const fileDataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(avatarFile);
      });

      const requestData = {
        recordId: recordId,
        fileName: avatarFile.name,
        fileData: fileDataUrl
      };

      const response = await fetch('/api/upload-avatar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        let errorMessage = 'Avatar upload failed';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.details || errorMessage;
          console.error('Avatar upload error details:', errorData);
        } catch (parseError) {
          const errorText = await response.text();
          console.error('Avatar upload error (raw):', errorText);
          errorMessage = `Avatar upload failed (${response.status})`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result.avatarUrl;
    } catch (error) {
      console.error('Avatar upload error:', error);
      // Don't fail the entire form submission if avatar upload fails
      toast({
        title: t('common.warning'),
        description: "Form submitted successfully, but avatar upload failed. You can try uploading it again later.",
        variant: "destructive",
      });
      return null;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Step 1: Submit form data
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...formData, formType: "worker" }),
      });

      const result = await response.json();

      if (response.ok) {
        // Step 2: Upload avatar if provided
        if (avatarFile && result.recordId) {
          await uploadAvatar(result.recordId);
        }

        toast({
          title: t('worker_form.success_title'),
          description: t('worker_form.success_description'),
          variant: "success",
        });

        // Reset form
        setFormData({
          FirstName: "",
          LastName: "",
          Email: "",
          PhoneNumber: "",
          City: "",
          Nationality: "",
          AvailabilityDate: "",
          WillingToTravel: "",
          SalaryExpectation: "",
          DesiredPosition: "",
          EmploymentType: "",
          Job1_Title: "",
          Job1_Company: "",
          Job1_Duration: "",
          Job1_Tasks: "",
          Job1_Start: "",
          Job1_End: "",
          Job2_Title: "",
          Job2_Company: "",
          Job2_Duration: "",
          Job2_Tasks: "",
          Job2_Start: "",
          Job2_End: "",
          Job3_Title: "",
          Job3_Company: "",
          Job3_Duration: "",
          Job3_Tasks: "",
          Job3_Start: "",
          Job3_End: "",
          EducationLevel: "",
          // Single education entry without "1" suffix to match NocoDB structure
          Education_Start: "",
          Education_End: "",
          Education_School: "",
          Education_Detail: "",
          GermanLevel: "",
          OtherLanguages: "",
          DrivingLicense: "",
          SkillsAndCerts: "",
          Language: language,
          formType: "worker"
        });
        setAvatarFile(null);
        setAvatarPreview(null);
        setJobCount(1); // Reset to show only first job
      } else {
        throw new Error(result.message || 'Submission failed');
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={combineClasses(tokens.card.elevated, themeClasses.formSection)}>
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl">{t('worker_form.title')}</CardTitle>
            <p className="text-muted-foreground">{t('worker_form.subtitle')}</p>
            <p className="text-sm text-muted-foreground mt-2">
              <span className="text-red-500">*</span> {t('common.required')}
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground border-b pb-2">{t('worker_form.personal_info')}</h3>

                {/* Avatar Upload Section */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="avatar">{t('worker_form.fields.avatar')} <span className="text-red-500">*</span> </Label>
                    <div className="flex items-center space-x-4">
                      {avatarPreview ? (
                        <div className="relative">
                          <img
                            src={avatarPreview}
                            alt="Avatar preview"
                            className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                          />
                          <button
                            type="button"
                            onClick={removeAvatar}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                          <span className="text-gray-400 text-xs text-center">No Image</span>
                        </div>
                      )}
                      <div className="flex-1">
                        <input
                          id="avatar"
                          type="file"
                          accept="image/jpeg,image/jpg,image/png"
                          onChange={handleAvatarChange}
                          required
                          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {t('worker_form.avatar_help')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {language === 'zh' ? (
                    // Chinese: Last name (surname) first, then first name (given name)
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">{t('worker_form.fields.last_name')} <span className="text-red-500">*</span></Label>
                        <Input
                          id="lastName"
                          value={formData.LastName}
                          onChange={(e) => handleInputChange('LastName', e.target.value)}
                          placeholder={t('worker_form.placeholders.last_name')}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="firstName">{t('worker_form.fields.first_name')} <span className="text-red-500">*</span></Label>
                        <Input
                          id="firstName"
                          value={formData.FirstName}
                          onChange={(e) => handleInputChange('FirstName', e.target.value)}
                          placeholder={t('worker_form.placeholders.first_name')}
                          required
                        />
                      </div>
                    </>
                  ) : (
                    // Other languages: First name first, then last name
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="firstName">{t('worker_form.fields.first_name')} <span className="text-red-500">*</span></Label>
                        <Input
                          id="firstName"
                          value={formData.FirstName}
                          onChange={(e) => handleInputChange('FirstName', e.target.value)}
                          placeholder={t('worker_form.placeholders.first_name')}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">{t('worker_form.fields.last_name')} <span className="text-red-500">*</span></Label>
                        <Input
                          id="lastName"
                          value={formData.LastName}
                          onChange={(e) => handleInputChange('LastName', e.target.value)}
                          placeholder={t('worker_form.placeholders.last_name')}
                          required
                        />
                      </div>
                    </>
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="email">{t('worker_form.fields.email')} <span className="text-red-500">*</span></Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.Email}
                      onChange={(e) => handleInputChange('Email', e.target.value)}
                      placeholder={t('worker_form.placeholders.email')}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality">{t('worker_form.fields.nationality')} <span className="text-red-500">*</span></Label>
                    <Input
                      id="nationality"
                      type="text"
                      value={formData.Nationality}
                      onChange={(e) => handleInputChange('Nationality', e.target.value)}
                      placeholder={t('worker_form.placeholders.nationality')}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">{t('worker_form.fields.phone')} <span className="text-red-500">*</span></Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      value={formData.PhoneNumber}
                      onChange={(e) => handleInputChange('PhoneNumber', e.target.value)}
                      placeholder={t('worker_form.placeholders.phone')}
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      German format: +49 123 456789 (automatically formatted)
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">{t('worker_form.fields.city')} <span className="text-red-500">*</span></Label>
                    <Input
                      id="city"
                      type="text"
                      value={formData.City}
                      onChange={(e) => handleInputChange('City', e.target.value)}
                      placeholder={t('worker_form.placeholders.city')}
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Job Expectations */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground border-b pb-2">{t('worker_form.job_expectations')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="desiredPosition">{t('worker_form.fields.desired_position')} <span className="text-red-500">*</span></Label>
                    <Input
                      id="desiredPosition"
                      value={formData.DesiredPosition}
                      onChange={(e) => handleInputChange('DesiredPosition', e.target.value)}
                      placeholder={t('worker_form.placeholders.desired_position')}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="employmentType">{t('worker_form.fields.employment_type')} <span className="text-red-500">*</span></Label>
                    <Select value={formData.EmploymentType} onValueChange={(value) => handleInputChange('EmploymentType', value)} required>
                      <SelectTrigger>
                        <SelectValue placeholder={t('worker_form.placeholders.employment_type')} />
                      </SelectTrigger>
                      <SelectContent>
                        {formOptions.employmentTypes.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="availabilityDate">{t('worker_form.fields.availability_date')}</Label>
                    <Input
                      id="availabilityDate"
                      type="text"
                      value={formData.AvailabilityDate}
                      onChange={(e) => handleDateInput('AvailabilityDate', e.target.value)}
                      placeholder="DD/MM/YYYY"
                      maxLength={10}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="willingToTravel">{t('worker_form.fields.willing_to_travel')}</Label>
                    <Select value={formData.WillingToTravel} onValueChange={(value) => handleInputChange('WillingToTravel', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('worker_form.placeholders.willing_to_travel')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">{t('common.yes')}</SelectItem>
                        <SelectItem value="no">{t('common.no')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="salaryExpectation">{t('worker_form.fields.salary_expectation')}</Label>
                    <Input
                      id="salaryExpectation"
                      type="number"
                      value={formData.SalaryExpectation}
                      onChange={(e) => handleInputChange('SalaryExpectation', e.target.value)}
                      placeholder={t('worker_form.placeholders.salary_expectation')}
                    />
                  </div>
                </div>
              </div>

              {/* Work History */}
              <div className="space-y-4">
                <div className="flex justify-between items-center border-b pb-2">
                  <h3 className="text-lg font-semibold text-foreground">{t('worker_form.work_history')}</h3>
                  {jobCount < 3 && (
                    <button
                      type="button"
                      onClick={addJobEntry}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      + {t('worker_form.add_job')}
                    </button>
                  )}
                </div>
                <div className="space-y-4">
                  {Array.from({ length: jobCount }, (_, index) => renderJobEntry(index + 1))}
                </div>
              </div>

              {/* Skills & Qualifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground border-b pb-2">{t('worker_form.skills_qualifications')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="educationLevel">{t('worker_form.fields.education_level')} <span className="text-red-500">*</span></Label>
                    <Select value={formData.EducationLevel} onValueChange={(value) => handleInputChange('EducationLevel', value)} required>
                      <SelectTrigger>
                        <SelectValue placeholder={t('worker_form.placeholders.education_level')} />
                      </SelectTrigger>
                      <SelectContent>
                        {formOptions.educationLevels.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Education Details - Only show if not "no_degree" */}
                {formData.EducationLevel && formData.EducationLevel !== 'no_degree' && (
                  <div className="space-y-4">
                    {renderEducationEntry()}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="germanLevel">{t('worker_form.fields.german_level')} <span className="text-red-500">*</span></Label>
                    <Select value={formData.GermanLevel} onValueChange={(value) => handleInputChange('GermanLevel', value)} required>
                      <SelectTrigger>
                        <SelectValue placeholder={t('worker_form.placeholders.german_level')} />
                      </SelectTrigger>
                      <SelectContent>
                        {formOptions.germanLevels.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="otherLanguages">{t('worker_form.fields.other_languages')}</Label>
                    <Input
                      id="otherLanguages"
                      value={formData.OtherLanguages}
                      onChange={(e) => handleInputChange('OtherLanguages', e.target.value)}
                      placeholder={t('worker_form.placeholders.other_languages')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="drivingLicense">{t('worker_form.fields.driving_license')}</Label>
                    <Select value={formData.DrivingLicense} onValueChange={(value) => handleInputChange('DrivingLicense', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('worker_form.placeholders.driving_license')} />
                      </SelectTrigger>
                      <SelectContent>
                        {formOptions.drivingLicenses.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="md:col-span-2 space-y-2">
                    <Label htmlFor="skillsAndCerts">{t('worker_form.fields.skills_certs')}</Label>
                    <Textarea
                      id="skillsAndCerts"
                      value={formData.SkillsAndCerts}
                      onChange={(e) => handleInputChange('SkillsAndCerts', e.target.value)}
                      placeholder={t('worker_form.placeholders.skills_certs')}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Button
                  type="submit"
                  size="lg"
                  className={combineClasses("flex-1", tokens.button.primary, themeClasses.ctaButton)}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  {isSubmitting ? t('common.submitting') : t('common.submit')}
                </Button>
              </div>

              <div className="text-center pt-4">
                <p className="text-sm text-muted-foreground">
                  {t('forms.footer_agreement')}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm"
                    onClick={() => window.open(language === 'de' ? '/privacy-policy-de' : '/privacy-policy', '_blank')}
                  >
                    {t('navigation.privacy_policy')}
                  </Button>{" "}
                  {t('forms.and')}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm"
                    onClick={() => window.open(language === 'de' ? '/terms-of-service-de' : '/terms-of-service', '_blank')}
                  >
                    {t('navigation.terms_of_service')}
                  </Button>
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
  );
};

export default WorkerFormSection;
