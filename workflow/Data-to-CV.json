{"name": "Data-to-CV", "nodes": [{"parameters": {"httpMethod": "POST", "path": "nocodb-new-row", "options": {}}, "id": "0b3a9f16-f174-4973-ac13-9a1e36c7ce05", "name": "NocoDB Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-2840, 160], "webhookId": "YOUR_WEBHOOK_ID"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "3394c8e7-ff47-49d8-918d-61614ccb19ed", "leftValue": "={{ $json.body.data.resume_pdf_url }}", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "5a37a2ba-821e-4f01-bcc3-9e396ad48304", "leftValue": "={{ $json.body.data.rows[0].ResumeFile }}", "rightValue": "null", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "098c59eb-200f-4d77-861d-9dec955a2c56", "name": "IF: PDF exists?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2600, 160]}, {"parameters": {"url": "={{ $json.body.data.resume_pdf_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "17c3c5ba-6804-4ed0-9744-6534601ed457", "name": "Download PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-2360, 40]}, {"parameters": {"operation": "pdf", "options": {}}, "id": "0abb355d-0111-4496-8c05-8b344f48723b", "name": "Extract Text from PDF", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-2120, 40]}, {"parameters": {"prompt": "={{ $json.text }}", "messages": {"messageValues": [{"message": "Your task is to extract all necessary data such as first name, last name, experience, known technologies etc. from the provided resume text and return in well-unified JSON format. Do not make things up."}]}}, "id": "975b0c74-293f-4334-9c06-cc04ebb79c4d", "name": "AI解析简历文本", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.3, "position": [-1880, 40]}, {"parameters": {}, "id": "9f85d18e-45c2-41d9-b3c7-3b66acf0c5a3", "name": "NoOp", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-2360, 300]}, {"parameters": {"fields": {"values": [{"name": "finalResumeData"}]}, "options": {}}, "id": "c3c0e66c-56e9-432b-955f-8eb657a6e9cb", "name": "Set: 合并数据", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [-1580, 40]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// 1. 从NocoDB Webhook的输入中提取核心数据\nconst data = $input.item.json;\n\n// 2. (辅助函数) 创建一些帮助函数来翻译和格式化数据\nconst helpers = {\n    translateEducation: (level) => {\n        const map = {\n            'high_school': 'Hochschulreife / Abitur',\n            'bachelors_degree': 'Bachelor-Abschluss',\n            'masters_degree': 'Master-Abschluss',\n            'doctorate': 'Doktortitel / PhD'\n        };\n        return map[level] || level || 'Keine Angabe';\n    },\n    translateDrivingLicense: (license) => {\n        const map = {\n            'class_b': 'Klasse B',\n            'class_c': 'Klasse C',\n            'class_d': 'Klasse D'\n        };\n        return map[license] || license || '';\n    },\n    createWorkExperienceHtml: () => {\n        let html = '';\n        for (let i = 1; i <= 3; i++) {\n            const title = data[`Job${i}_Title`];\n            const company = data[`Job${i}_Company`];\n            const duration = data[`Job${i}_Duration`];\n            const tasks = data[`Job${i}_Tasks`];\n\n            if (title) {\n                html += `\n                    <li class=\"timeline-item\">\n                        <span class=\"timeline-date\">${duration || ''}</span>\n                        <h3 class=\"timeline-title\">${title}</h3>\n                        <p class=\"timeline-subtitle\">${company || ''}</p>\n                        <ul class=\"timeline-tasks\">\n                            <li>${tasks || ''}</li>\n                        </ul>\n                    </li>\n                `;\n            }\n        }\n        return html;\n    }\n};\n\n// 3. 完整的HTML模板，使用了上面的数据和辅助函数\nconst finalHtml = `\n<!DOCTYPE html>\n<html lang=\"de\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Lebenslauf - ${data.FirstName} ${data.LastName}</title>\n    <style>\n        /* ===== 全局和页面样式 ===== */\n        html, body {\n            margin: 0;\n            padding: 0;\n            width: 100%;\n            height: 100%;\n            font-family: Arial, sans-serif;\n            font-size: 10pt;\n            background-color: #fff;\n        }\n        .resume-container {\n            display: flex;\n            width: 100%;\n            height: 100%;\n        }\n        \n        /* ===== 列和内容样式 ===== */\n        .left-column {\n            background-color: #f8f9fa;\n            width: 250px;\n            padding: 50px 20px 30px 20px; /* 增加了顶部内边距 */\n            color: #333;\n            box-sizing: border-box;\n        }\n        .right-column {\n            flex-grow: 1;\n            padding: 25px 30px 30px 30px; /* 增加了顶部内边距 */\n            box-sizing: border-box;\n        }\n        .profile-pic {\n            width: 120px;\n            height: 120px;\n            border-radius: 50%;\n            margin: 0 auto 20px auto;\n            display: block;\n            background-color: #e0e0e0;\n            border: 1px solid #ccc;\n        }\n        .left-column h1 {\n            font-size: 24pt;\n            text-align: center;\n            margin-bottom: 30px;\n            color: #000;\n            word-break: break-word;\n        }\n        .contact-info p { margin: 10px 0; line-height: 1.4; word-wrap: break-word; }\n        .contact-info strong { display: block; color: #555; font-size: 9pt; }\n        .section-header { font-size: 13pt; color: #6c757d; text-transform: uppercase; font-weight: bold; margin-bottom: 20px; padding-bottom: 5px; border-bottom: 1px solid #dee2e6; }\n        \n        /* ===== 时间轴样式修改 ===== */\n        .timeline { position: relative; list-style: none; padding-left: 20px; margin:0; border-left: 2px solid #004a99; }\n        .timeline-item { margin-bottom: 25px; position: relative; }\n        .timeline-item::before { content: ''; position: absolute; left: -28px; top: 4px; width: 12px; height: 12px; background-color: #004a99; border-radius: 50%; border: 2px solid #fff; }\n        .timeline-date { font-weight: bold; color: #004a99; font-size: 9pt; display: block; }\n        \n        .timeline-title { font-weight: bold; font-size: 11pt; margin: 5px 0; }\n        .timeline-subtitle { color: #333; margin-bottom: 8px; }\n        .timeline-tasks { padding-left: 20px; margin-top: 5px; }\n        .timeline-tasks li { margin-bottom: 5px; }\n        .skills-section { margin-top: 25px; }\n        \n        /* ===== 新增的Logo样式 ===== */\n        .logo-header {\n            text-align: right;\n            margin-bottom: 40px; /* Logo和下方内容的间距 */\n        }\n        .logo-img {\n            height: 25px; /* 控制logo的高度 */\n        }\n    </style>\n</head>\n<body>\n<div class=\"resume-container\">\n    <div class=\"left-column\">\n        <div class=\"profile-pic\"></div>\n        <h1>${data.FirstName || ''} ${data.LastName || ''}</h1>\n        <div class=\"contact-info\">\n            <p><strong>Nationalität:</strong>${data.Nationality || 'Nicht angegeben'}</p>\n            <p><strong>Telefonnummer:</strong>${data.PhoneNumber || ''}</p>\n            <p><strong>E-Mail-Adresse:</strong>${data.Email || ''}</p>\n            <p><strong>Adresse:</strong>${data.City || ''}</p>\n        </div>\n    </div>\n    <div class=\"right-column\">\n        \n        <div class=\"logo-header\">\n            <img src=\"https://storage.googleapis.com/sohnus-logo/Logo-text-dark.png\" alt=\"logo\" class=\"logo-img\">\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-header\">Angestrebte Position</h2>\n            <p>${data.DesiredPosition || ''}</p>\n        </div>\n        <div class=\"section\">\n            <h2 class=\"section-header\">Berufserfahrung</h2>\n            <ul class=\"timeline\">\n                ${helpers.createWorkExperienceHtml()}\n            </ul>\n        </div>\n        <div class=\"section\">\n            <h2 class=\"section-header\">Bildungsweg</h2>\n            <ul class=\"timeline\">\n                <li class=\"timeline-item\">\n                     <h3 class=\"timeline-title\">${helpers.translateEducation(data.EducationLevel)}</h3>\n                </li>\n            </ul>\n        </div>\n        <div class=\"section skills-section\">\n            <h2 class=\"section-header\">Sprachkenntnisse</h2>\n            <p><strong>Deutsch:</strong> ${data.GermanLevel ? data.GermanLevel.toUpperCase() : ''}</p>\n            <p><strong>Weitere Sprachen:</strong> ${data.OtherLanguages || ''}</p>\n        </div>\n        <div class=\"section skills-section\">\n            <h2 class=\"section-header\">Weitere Kenntnisse</h2>\n            <p><strong>Führerschein:</strong> ${helpers.translateDrivingLicense(data.DrivingLicense)}</p>\n            <p><strong>Fähigkeiten & Zertifikate:</strong> ${data.SkillsAndCerts || ''}</p>\n        </div>\n    </div>\n</div>\n</body>\n</html>\n`;\n\n// 4. 返回最终的HTML内容\nreturn { finalHtml: finalHtml };"}, "id": "1037a87f-9cd0-4d98-9a16-0c80a655192f", "name": "Code: 生成完整HTML", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-240, 200]}, {"parameters": {"operation": "toText", "sourceProperty": "finalHtml", "options": {"fileName": "index.html"}}, "id": "aeb000b5-d097-4526-86bc-17825b5bc6f5", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [20, 200]}, {"parameters": {"method": "POST", "url": "https://gotenberg-544103549891.europe-west3.run.app/forms/chromium/convert/html", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "files", "inputDataFieldName": "data"}, {"name": "marginTop", "value": "0"}, {"name": "marginBottom", "value": "0"}, {"name": "marginLeft", "value": "0"}, {"name": "marginRight", "value": "0"}]}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "********-f768-4ae8-9cd8-76dc4ddacc00", "name": "调用Gotenberg生成PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [280, 200], "alwaysOutputData": false}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1800, 240], "id": "422ce69f-c2b6-49f6-9828-db94d6ba0516", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1900, 220], "id": "ec8c08f6-ff30-4272-aa30-d2fd0588f6dc", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "={{ JSON.stringify($json.dataToTranslate) }}", "messages": {"messageValues": [{"message": "You are a precise data processing API. Your sole function is to receive a JSON object, translate the values of its properties into German, and return ONL<PERSON> the modified JSON object.\n\nFollow these rules strictly:\n1.  Your entire output must be a single, valid JSON object.\n2.  DO NOT wrap the output in Markdown code blocks like ```json ... ```.\n3.  DO NOT add any explanatory text, greetings, or apologies before or after the JSON.\n4.  PRESERVE the original JSON keys and structure exactly.\n5.  If a value is an email, a URL, a date, a number, or a technical code (e.g., 'b2', 'class_b'), DO NOT translate it.\n6.  Only translate descriptive text like job titles, tasks, and city names (e.g., translate \"软件开发工程师\" to \"Softwareentwickler\").\n7.  In case of Chinese names and company names, translate them into Pinyin."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-920, 200], "id": "0b834b83-f253-4b5c-ab18-b8392efda99f", "name": "CV Content Translator", "alwaysOutputData": false}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-880, 420], "id": "990622ea-9867-40ae-94df-025c144a19ec", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "db04dfa0-acb8-4d84-880e-ddc756202340", "name": "dataToTranslate", "value": "={{ $json.body.data.rows[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1220, 200], "id": "e776c3b4-efa3-4b88-81ea-a4c5f7ef104e", "name": "提取待翻译数据"}, {"parameters": {"jsCode": "// 上一个AI节点的输出是一个像 { \"text\": \"{\\\"Id\\\":1, ...}\" } 这样的对象。\n// 我们首先需要获取 \"text\" 属性的值，它是一个包含了JSON内容的字符串。\nconst jsonString = $input.item.json.text;\n\n// 使用JavaScript内置的 JSON.parse() 函数，\n// 将这个字符串转换回一个真正的、可操作的JSON对象。\nconst parsedData = JSON.parse(jsonString);\n\n// 返回这个解析后的JSON对象，给工作流中的下一个节点使用。\nreturn parsedData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 200], "id": "f8cad3b7-20a0-4ebc-baea-47270cb31b83", "name": "解包"}], "pinData": {}, "connections": {"NocoDB Trigger": {"main": [[{"node": "IF: PDF exists?", "type": "main", "index": 0}]]}, "IF: PDF exists?": {"main": [[{"node": "Download PDF", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Download PDF": {"main": [[{"node": "Extract Text from PDF", "type": "main", "index": 0}]]}, "Extract Text from PDF": {"main": [[{"node": "AI解析简历文本", "type": "main", "index": 0}]]}, "AI解析简历文本": {"main": [[{"node": "Set: 合并数据", "type": "main", "index": 0}]]}, "NoOp": {"main": [[{"node": "提取待翻译数据", "type": "main", "index": 0}]]}, "Set: 合并数据": {"main": [[{"node": "提取待翻译数据", "type": "main", "index": 0}]]}, "Code: 生成完整HTML": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "调用Gotenberg生成PDF", "type": "main", "index": 0}]]}, "调用Gotenberg生成PDF": {"main": [[]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI解析简历文本", "type": "ai_languageModel", "index": 1}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "AI解析简历文本", "type": "ai_languageModel", "index": 0}]]}, "CV Content Translator": {"main": [[{"node": "解包", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "CV Content Translator", "type": "ai_languageModel", "index": 0}]]}, "提取待翻译数据": {"main": [[{"node": "CV Content Translator", "type": "main", "index": 0}]]}, "解包": {"main": [[{"node": "Code: 生成完整HTML", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6aff2b83-c57a-44ef-b06f-343ca035782d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36da3f6eda1d57115c10d4e4bd55c0be5401554fa8cab2d07c58a7aea769a71f"}, "id": "Y4QFm38TjeDRlQC8", "tags": []}